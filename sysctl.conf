# sysctl.conf - 网络内核参数配置文件
# 启用IPv6配置

# 基本的网络优化参数
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_mem = 16777216 16777216 16777216

# IPv6配置 - 启用IPv6（删除或注释掉禁用IPv6的配置）
# 注意：以下所有禁用IPv6的配置都应该被注释掉或删除
# net.ipv6.conf.all.disable_ipv6 = 1      # 已注释 - 原来是禁用IPv6
# net.ipv6.conf.default.disable_ipv6 = 1  # 已注释 - 原来是禁用IPv6
# net.ipv6.conf.lo.disable_ipv6 = 1       # 已注释 - 原来是禁用IPv6
# net.ipv6.conf.all.autoconf = 0          # 已注释 - 原来是禁用自动配置
# net.ipv6.conf.all.accept_ra = 0         # 已注释 - 原来是禁用路由器通告
# net.ipv6.conf.eth0.autoconf = 0         # 已注释 - 原来是禁用eth0自动配置
# net.ipv6.conf.eth0.accept_ra = 0        # 已注释 - 原来是禁用eth0路由器通告

# 如果需要特定的IPv6配置，可以取消以下注释并根据需要调整：
# net.ipv6.conf.all.forwarding = 1        # 启用IPv6转发（如果需要路由器功能）
# net.ipv6.conf.all.autoconf = 1          # 启用自动配置
# net.ipv6.conf.all.accept_ra = 1         # 接受路由器通告
# net.ipv6.conf.all.accept_ra_rt_info_max_plen = 128  # 接受路由信息的最大前缀长度