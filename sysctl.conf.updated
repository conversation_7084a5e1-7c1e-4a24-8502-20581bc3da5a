# sysctl.conf - 网络内核参数配置文件
# 针对IPv6的推荐配置

# 基本的网络优化参数
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_mem = 16777216 16777216 16777216

# IPv6配置 - 启用IPv6（确保没有禁用IPv6的配置）
# 重要：确保没有以下禁用IPv6的配置
# net.ipv6.conf.all.disable_ipv6 = 1      # 必须删除或注释掉
# net.ipv6.conf.default.disable_ipv6 = 1  # 必须删除或注释掉
# net.ipv6.conf.lo.disable_ipv6 = 1       # 必须删除或注释掉

# 关于自动配置的建议（根据您的情况）：
# 由于您已经在网络接口配置文件中手动配置了IPv6地址和路由，
# 建议保持自动配置禁用，以避免冲突：

#net.ipv6.conf.all.autoconf = 0          # 保持注释（禁用自动配置）
#net.ipv6.conf.all.accept_ra = 0         # 保持注释（禁用路由器通告）
#net.ipv6.conf.eth0.autoconf = 0         # 保持注释（禁用eth0自动配置）
#net.ipv6.conf.eth0.accept_ra = 0        # 保持注释（禁用eth0路由器通告）

# 如果您希望启用自动配置，可以取消注释并设置为1：
# net.ipv6.conf.all.autoconf = 1
# net.ipv6.conf.all.accept_ra = 1
# net.ipv6.conf.eth0.autoconf = 1
# net.ipv6.conf.eth0.accept_ra = 1

# 其他有用的IPv6配置（可选）：
# net.ipv6.conf.all.forwarding = 0        # 禁用转发（除非您需要路由器功能）
# net.ipv6.conf.default.accept_ra_rt_info_max_plen = 128